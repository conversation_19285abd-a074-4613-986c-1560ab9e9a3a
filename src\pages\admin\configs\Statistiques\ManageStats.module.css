/* Styles pour la page de statistiques - Design simple et professionnel */

.statsCard {
  transition: all 0.2s ease;
}

.statsCard:hover {
  border-color: #d9d9d9;
}

.chartContainer {
  transition: all 0.2s ease;
}

.chartContainer:hover {
  border-color: #d9d9d9;
}

/* Style pour la section des filtres avec background */
.filtersSection {
  position: relative;
  overflow: hidden;
}

 

.filtersSection .ant-card-body {
  position: relative;
  z-index: 2;
}

.filtersSection .ant-collapse {
  position: relative;
  z-index: 2;
}

.modernTable .ant-table {
  border-radius: 6px;
}

.modernTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  color: #262626;
}

.modernTable .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* Animation pour l'apparition des éléments */
.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles pour le collapse */
.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
  padding: 12px 0;
  font-weight: 500;
}

.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 16px 0;
}