import { Modal, Form, Input, Row, Col, Select, DatePicker, Switch, Spin } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from 'dayjs';
import { useEffect, useState, useRef } from "react";

interface ClientFormModalProps {
    modalVisible: boolean;
    onCancel: () => void;
    onSubmit: (values: any) => void;
    viewMode?: boolean;
    editingClient?: any;
    clientTypes: any[];
    establishments: any[];
    schoolDegrees: any[];
    governorates: any[];
    filteredDelegations: any[];
    isDelegationsLoading: boolean;
    selectedClientType?: any;
    onClientTypeChange: (value: number) => void;
    onGovernorateChange: (governorateId: number) => void;
    onEstablishmentChange?: (establishmentId: number) => void;
    selectedFilterGovernorate?: number | null;
    currentLang: string;
    initialValues?: any;
    isFromSubs?: boolean;
    fieldErrors?: Array<{ name: string; errors: string[] }>;
    loading?: boolean;
    form?: any;
}

const ClientFormModal: React.FC<ClientFormModalProps> = ({
    modalVisible,
    onCancel,
    onSubmit,
    viewMode = false,
    editingClient = null,
    clientTypes,
    establishments,
    schoolDegrees,
    governorates,
    filteredDelegations,
    isDelegationsLoading,
    selectedClientType,
    onClientTypeChange,
    onGovernorateChange,
    selectedFilterGovernorate,
    currentLang,
    initialValues = {},
    isFromSubs,
    fieldErrors = [],
    loading = false,
    form: externalForm, // Accept external form from props
}) => {
    const { t } = useTranslation();
    // Use external form if provided, otherwise create a local form instance
    const [internalForm] = Form.useForm();
    const form = externalForm || internalForm;
    const [isPasswordEditable, setPasswordEditable] = useState(false);

    // Use a ref to track if we've already initialized the form for the current modal session
    const formInitialized = useRef(false);

    useEffect(() => {
        if (!modalVisible) {
            form.resetFields();
            setPasswordEditable(false);
            formInitialized.current = false; // Reset the initialization flag when modal closes
        } else if (modalVisible && initialValues && Object.keys(initialValues).length > 0 && !formInitialized.current) {
            // Set the flag to prevent re-initialization
            formInitialized.current = true;

            // Make sure we're using the correct date format for dob
            const formattedValues = { ...initialValues };
            if (formattedValues.dob && !dayjs.isDayjs(formattedValues.dob)) {
                formattedValues.dob = dayjs(formattedValues.dob);
            }

            // First, trigger governorate change to load delegations if needed
            if (initialValues.id_governorate) {
                onGovernorateChange(initialValues.id_governorate);
            }

            // Set client type to ensure proper field visibility
            if (initialValues.id_client_type) {
                onClientTypeChange(initialValues.id_client_type);
            }

            // Set all form values after a short delay to ensure delegations are loaded
            setTimeout(() => {
                form.setFieldsValue(formattedValues);
            }, 100);
        }
    }, [modalVisible, form, initialValues]);

    // Apply field errors when they change
    useEffect(() => {
        if (fieldErrors && fieldErrors.length > 0) {
            form.setFields(fieldErrors);
        }
    }, [fieldErrors, form]);

    return (
        <Modal
            width={900}
            title={
                viewMode
                    ? t("manage_users.client.details_client")
                    : editingClient
                        ? t("manage_users.client.edit_client")
                        : t("manage_users.client.add_client")
            }
            open={modalVisible}
            onCancel={() => {
                onCancel();
                form.resetFields();
            }}
            onOk={() => (viewMode ? onCancel() : form.submit())}
            okText={viewMode ? null : t("common.save")}
            footer={viewMode ? null : undefined}
        >
            {loading ? (
                <div className="flex justify-center items-center py-10">
                    <Spin />
                </div>
            ) : modalVisible ? (
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={onSubmit}
                    initialValues={editingClient ? {
                        ...editingClient,
                        id_degree: editingClient.degree?.id || null,
                        id_client_type: editingClient.client_type?.id || null,
                        id_governorate: editingClient.governorate?.id || null,
                        id_delegation: editingClient.delegation?.id || null,
                        id_establishment: editingClient.establishment?.id || null,
                        dob: editingClient.dob ? dayjs(editingClient.dob) : null
                    } : initialValues}
                >
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.lastname")}
                            name="lastname"
                            rules={[{ required: true, message: t("manage_users.errors.lastnameRequired") }]}
                        >
                            <Input
                                placeholder={t("manage_users.client.placeholders.lastname")}
                                disabled={viewMode}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.firstname")}
                            name="firstname"
                            rules={[{ required: true, message: t("manage_users.errors.firstnameRequired") }]}
                        >
                            <Input
                                placeholder={t("manage_users.client.placeholders.firstname")}
                                disabled={viewMode}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12}>
                        <Form.Item
                            label={t("manage_users.labels.phone")}
                            name="phone"
                            rules={[
                                { required: true, message: t("manage_users.errors.phoneRequired") },
                                {
                                    pattern: /^[0-9]{8}$/,
                                    message: t("manage_users.errors.phoneInvalid"),
                                },
                            ]}
                        >
                            <Input
                                placeholder={t("manage_users.client.placeholders.phone")}
                                disabled={viewMode}
                            />
                        </Form.Item>
                    </Col>

                    <Col xs={24} sm={12}>
                        <Form.Item
                            label={t("manage_users.labels.dob")}
                            name="dob"
                            rules={[
                                { required: true, message: t("manage_users.errors.dobRequired") }
                            ]}
                        >
                            <DatePicker
                                className="w-full"
                                format="YYYY-MM-DD"
                                placeholder={t("manage_users.client.placeholders.dob")}
                                minDate={dayjs().subtract(100, 'years')}
                                maxDate={dayjs().subtract(6, 'years')}
                                disabled={viewMode}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.clientType")}
                            name="id_client_type"
                            rules={[{ required: true, message: t("manage_users.errors.clientTypeRequired") }]}
                        >
                            <Select
                                onChange={onClientTypeChange}
                                disabled={isFromSubs || viewMode}
                                placeholder={t("manage_users.client.placeholders.clientType")}
                            >
                                {clientTypes?.map((el: any) => (
                                    <Select.Option key={el.id} value={el.id}>
                                        {el[`nom_${currentLang}`]}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={
                                selectedClientType && selectedClientType?.hasCIN
                                    ? t("manage_users.labels.cin")
                                    : selectedClientType?.is_impersonal ?
                                      t("manage_users.labels.identityNumber")
                                    : selectedClientType?.is_conventional
                                    ? t("manage_users.labels.conventionNumber")
                                    : selectedClientType?.is_student
                                    ? t("manage_users.labels.matricule")
                                    : t("manage_users.labels.identityNumber")
                            }
                            name="identity_number"
                            rules={[
                                { required: true, message: t("manage_users.errors.cinRequired") },
                                {
                                    pattern: /^[0-9]{8}$/,
                                    message: t("manage_users.errors.cinInvalid"),
                                },
                            ]}
                        >
                            <Input placeholder={
                                selectedClientType && selectedClientType?.hasCIN
                                    ? t("manage_users.client.placeholders.cin")
                                    : t("manage_users.client.placeholders.matricule")
                            } disabled={viewMode} />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.address")}
                            name="address"
                            rules={[{ required: true, message: t("manage_users.errors.addressRequired") }]}
                        >
                            <Input
                                placeholder={t("manage_users.client.placeholders.address")}
                                disabled={viewMode}
                            />
                        </Form.Item>
                    </Col>

                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.email")}
                            name="email"
                            rules={[
                                { required: true, type: "email", message: t("manage_users.errors.emailRequired") },
                            ]}
                        >
                            <Input placeholder={t("manage_users.client.placeholders.email")} disabled={viewMode} />
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col xs={24} sm={12}>
                        <Form.Item
                            name="id_governorate"
                            label={t("manage_users.labels.governorate")}
                            rules={[{ required: true, message: t("manage_users.errors.governorateRequired") }]}
                        >
                            <Select
                                disabled={viewMode}
                                placeholder={t("manage_users.placeholders.governorate")}
                                onChange={onGovernorateChange}
                                options={governorates?.map((gov: any) => ({
                                    label: gov[`nom_${currentLang}`],
                                    value: gov.id,
                                }))}
                            />
                        </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Form.Item
                            name="id_delegation"
                            label={t("manage_users.labels.delegation")}
                            rules={[{ required: true, message: t("manage_users.errors.delegationRequired") }]}
                        >
                            <Select
                                disabled={viewMode}
                                placeholder={t("manage_users.client.placeholders.delegation")}
                                options={filteredDelegations.map((del: any) => ({
                                    label: del[`nom_${currentLang}`],
                                    value: del.id,
                                }))}
                                notFoundContent={
                                    isDelegationsLoading ? (
                                        <div className="flex items-center justify-center py-2">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                        </div>
                                    ) : (
                                        <div className="text-center py-2 text-gray-500">
                                            {!selectedFilterGovernorate
                                                ? t("manage_users.selectGovernorate")
                                                : t("common.noData")}
                                        </div>
                                    )
                                }
                            />
                        </Form.Item>
                    </Col>
                </Row>

                {selectedClientType && selectedClientType?.is_student && (
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.establishment")}
                                name="id_establishment"
                                rules={[{ required: true, message: t("manage_users.errors.establishmentRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_users.client.placeholders.establishment")}
                                    onChange={(value) => onEstablishmentChange && onEstablishmentChange(value)}
                                >
                                    {establishments.map((el: any) => (
                                        <Select.Option key={el.id} value={el.id}>{el[`nom_${currentLang}`]}</Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={t("manage_users.labels.schoolDegree")}
                                name="id_degree"
                                rules={[{ required: true, message: t("manage_users.errors.schoolDegreeRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_users.client.placeholders.schoolDegree")}
                                >
                                    {schoolDegrees.map((el: any) => (
                                        <Select.Option key={el.id} value={el.id}>{el[`nom_${currentLang}`]}</Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                )}

                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label={t("manage_users.labels.password")}
                            name="password"
                        >
                            <div className="flex items-center gap-4">
                                <div className="flex-1">
                                    <Input.Password
                                        placeholder={t("manage_users.admin.placeholders.password")}
                                        disabled={!isPasswordEditable}
                                    />
                                </div>
                                <div>
                                    <Switch
                                        disabled={viewMode}
                                        checked={isPasswordEditable}
                                        onChange={(checked) => {
                                            setPasswordEditable(checked);
                                            if (!checked) {
                                                form.setFieldValue('password', '');
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                            {!viewMode && (
                                <small style={{ color: "var(--secondary-color)" }}>{t("manage_users.admin.defaultPasswordMessage")}</small>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                </Form>
            ) : null}
        </Modal>
    );
};

export default ClientFormModal;
