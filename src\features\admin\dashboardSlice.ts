import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = 'dashboard';

interface FilterParams {
    start_date?: string;
    end_date?: string;
}

const buildUrlWithParams = (endpoint: string, params: FilterParams): string => {
    let url = `statistics/${URL}`;
    const queryParams = [];

    if (params.start_date) {
        queryParams.push(`start_date=${params.start_date}`);
    }
    if (params.end_date) {
        queryParams.push(`end_date=${params.end_date}`);
    }

    if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
    }

    return url;
};

interface DashboardStats {
    total_subscriptions: number;
    monthly_subscriptions: number;
    total_revenue: number;
    monthly_revenue: number;
    total_clients: number;
    monthly_clients: number;
    subscription_status: Record<string, number>;
    subscriptions_by_type: any[];
}

interface StatisticsState {
    dashboard: DashboardStats | null;
    loading: boolean;
    error: string | null;
}

const initialState: StatisticsState = {
    dashboard: null,
    loading: false,
    error: null
};

export const getDashboardStats: any = createAsyncThunk(
    "getDashboardStats",
    async (params: FilterParams = {}, thunkAPI: any) => {
        try {
            const url = buildUrlWithParams('', params);
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

 

const dashboardSlice = createSlice({
    name: 'statistics',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getDashboardStats
            .addCase(getDashboardStats.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getDashboardStats.fulfilled, (state, action) => {
                state.loading = false;
                state.dashboard = action.payload;
            })
            .addCase(getDashboardStats.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
           
    }
});

export const { clearError } = dashboardSlice.actions;
export default dashboardSlice.reducer;
