import {useState, useEffect} from "react";
import {Minus<PERSON>ircle, RefreshCcw, RouteIcon} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
    Button,
    Modal,
    Form,
    Row,
    Col,
    Select,
    InputNumber,
    TimePicker,
    Card,
    Spin, Empty, Typography,
} from "antd";
import { PlusOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';

import moment from "moment";
import { RouteNameField } from "../index.ts";
import LineDisplay from "./LineDisplay.tsx";
import { getSeasons } from "../../features/admin/seasonSlice.ts";
import { assignLineStations, getLineStationsAndRoutes, updateAssignLineStation } from "../../features/admin/lineSlice.ts";
import { toast } from "react-toastify";
import {getStationsAll} from "../../features/admin/stationSlice.ts";

const LineStationsManager: any = ({ record }: any) => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const [stationForm] = Form.useForm();
    const [stationModalVisible, setStationModalVisible] = useState(false);
    const [selectedLine, setSelectedLine] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    const [isReversed, setIsReversed] = useState(false);

    const dispatch = useDispatch();
    const [seasonsData, setSeasonsData] = useState([]);
    const [stationsData, setStationsData] = useState([]);
    const [lineAllData, setLineAllData] = useState<any>([]);

    /*|--------------------------------------------------------------------------
    |  - FETCH ALL DATA
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        const fetchAllData = async () => {
            setLoading(true);
            try {
                const [seasonsResult, stationsResult, lineDataResult] = await Promise.all([
                    dispatch(getSeasons({
                        pageNumber: 1,
                        perPage: 100,
                        params: {},
                        sort: {},
                        filter: {}
                    })).unwrap(),
                    dispatch(getStationsAll()),
                    dispatch(getLineStationsAndRoutes(record.id))
                ]);

                setSeasonsData(seasonsResult.data);
                setStationsData(stationsResult.payload.data);
                setLineAllData(lineDataResult.payload);
            } catch (error) {
                console.error('Failed to fetch data:', error);
                toast.error(t("messages.error_loading"));
            } finally {
                setLoading(false);
            }
        };

        fetchAllData();
    }, [dispatch, record.id]);
    
    /*|--------------------------------------------------------------------------
    |  - ASSIGN OR UPDATE STATION OF A SELECTED LINE
    |-------------------------------------------------------------------------- */
    const handleStationFormSubmit = async (values: any) => {
        setLoading(true);
        const toastId: any = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        try {
            const { stations } = values;

            // Format stations data
            const separatedStations = stations.map(({ id, station_id, seasons, type, ...stationData }: any) => {
                const departure_times = Object.entries(seasons || {}).map(([seasonId, seasonData]: [string, any]) => ({
                    id_season: parseInt(seasonId),
                    times: (seasonData?.departure_times || [])
                        .filter(Boolean)
                        .map((time: moment.Moment) => time?.format('HH:mm'))
                        .filter(Boolean)
                })).filter(season => season.times.length > 0);

                return {
                    ...(id ? { id } : {}),
                    position: stationData.position,
                    id_station: station_id || stationData.id_station,
                    type: type,  // Include the type in the payload
                    departure_times
                };
            });

            // Format routes data
            const separatedRoutes = [];
            for (let i = 1; i < stations.length; i++) {
                const prevStation = stations[i - 1];
                const currentStation = stations[i];

                if (currentStation?.route?.number_of_km) {
                    const routePayload = {
                        ...(currentStation.route.id ? { id: currentStation.route.id } : {}),
                        number_of_km: currentStation.route.number_of_km,
                        id_station_start: prevStation.station_id || prevStation.id_station,
                        id_station_end: currentStation.station_id || currentStation.id_station,
                        inter_station: true,
                        status: true
                    };
                    separatedRoutes.push(routePayload);
                }
            }

            const payload = {
                id_line: selectedLine?.id,
                stations: separatedStations,
                routes: separatedRoutes
            };

            // Check if we're updating or creating
            const isUpdate = lineAllData?.stations?.some((station: any) => station.id);

            if (isUpdate) {
                await dispatch(updateAssignLineStation(payload)).unwrap();
            } else {
                await dispatch(assignLineStations(payload)).unwrap();
            }

            const updatedLineData = await dispatch(getLineStationsAndRoutes(selectedLine.id)).unwrap();
            setLineAllData(updatedLineData);

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });

            handleReset();
        } catch (error: any) {
            console.error("Form submission error:", error);
            toast.update(toastId, {
                render: error.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_lines.confirmAction"),
            content: t("manage_lines.confirmUpdate"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleStationFormSubmit(values);
            },
            centered: true,
        });
    }

    /*|--------------------------------------------------------------------------
    |  - INITIALISE STATION ROUTES DATA TO UPDATE
    |-------------------------------------------------------------------------- */
    const handleUpdateStations = (line: any) => {
        setStationModalVisible(true);
        setSelectedLine(line);

        // Si pas de stations ou routes, initializer aver une station vide
        if (!lineAllData?.stations?.length && !lineAllData?.routes?.length) {
            stationForm.setFieldsValue({
                stations: [
                    {
                        id_station: null,
                        position: 1,
                        type: "INTER",
                        seasons: seasonsData.reduce((acc: any, season: any) => ({
                            ...acc,
                            [season.id]: {
                                departure_times: [moment().startOf("hour")]
                            }
                        }), {})
                    },
                ],
            });
            return;
        }

        // Format existing stations data for the form
        const formattedStations = lineAllData.stations.map((station: any, index: number) => {
            // Format season times
            const seasonTimes: any = {};
            if (Array.isArray(station.departure_times)) {
                station.departure_times.forEach((dt: any) => {
                    if (dt && dt.id_season) {
                        seasonTimes[dt.id_season] = {
                            departure_times: dt.times.map((time: string) => moment(time, "HH:mm"))
                        };
                    }
                });
            }

            const formStation: any = {
                id: station.id,
                id_station: station.id,
                position: station.position,
                type: station.type,
                seasons: seasonTimes
            };

            // Find corresponding route for stations after the first one
            if (index > 0 && lineAllData.routes) {
                const route = lineAllData.routes.find((r: any) => 
                    r.station_depart.id === lineAllData.stations[index - 1].id &&
                    r.station_arrival.id === station.id
                );
                if (route) {
                    formStation.route = {
                        id: route.id,
                        number_of_km: parseFloat(route.number_of_km),
                        inter_station: route.inter_station,
                        status: true
                    };
                }
            }
            return formStation;
        });
        stationForm.setFieldsValue({
            stations: formattedStations
        });
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setStationModalVisible(false);
    };

    return (
        <div className="p-4">
            <div
                className="mb-8 flex flex-col sm:flex-row justify-between items-center gap-4 border-b border-red-100 pb-4">
                <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <span className="bg-red-100 p-2 rounded-lg">
                        <RouteIcon style={{color: "var(--primary-color)"}}/>
                    </span>
                    {t("manage_lines.stations")}
                </h4>
                <div className="flex gap-1">
                    <Button
                        icon={<RefreshCcw className="mr-1" size={12}/>}
                        onClick={() => setIsReversed((prev:any) => !prev)}
                    >
                        {isReversed ? t("manage_lines.direction.normal") : t("manage_lines.direction.reverse")}
                    </Button>
                    <Button
                        className="btn-edit"
                        onClick={() => handleUpdateStations(record)}
                    >
                        {t("manage_lines.editStation")}
                    </Button>
                </div>
            </div>

            <LineDisplay loading={loading} record={lineAllData} isReversed={isReversed}/>

            <Modal
                width={1000}
                style={{maxWidth: "100%"}}
                title={t("manage_lines.addStationAssignment")}
                open={stationModalVisible}
                onCancel={() => handleReset()}
                onOk={() => stationForm.submit()}
                okText={t("manage_lines.save")}
                destroyOnClose
            >
                <Form 
                    form={stationForm} 
                    layout="vertical" 
                    onFinish={confirmSubmit}
                    validateMessages={{
                        required: t("common.fieldRequired"),
                    }}
                >
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item>
                                <Form.List name="stations" rules={[
                                    {
                                        validator: async (_:any, stations:any) => {
                                            if (!stations || stations.length < 2) {
                                                return Promise.reject(new Error(t("manage_lines.errors.minimumStations")));
                                            }
                                        },
                                    },
                                ]}>
                                    {(fields: any, {add, remove}: any) => (
                                        <>
                                            <Row gutter={[16, 16]} style={{marginBottom: 8}}>
                                                <Col xs={10}>{t("manage_lines.labels.stations")}</Col>
                                                <Col xs={4}>{t("manage_lines.labels.position")}</Col>
                                            </Row>

                                            {fields.map(({key, name, ...restField}: any, index: number) => {
                                                return (
                                                    <div key={key}>
                                                        {index > 0 && (
                                                            <>
                                                                <div
                                                                    style={{
                                                                        marginBottom: 16,
                                                                        backgroundColor: '#fafafa',
                                                                        padding: 10,
                                                                        borderRadius: 8
                                                                    }}
                                                                >
                                                                    <h2 className="mb-6 border-b pb-3">
                                                                        {t("manage_lines.title_inter_station")}
                                                                    </h2>

                                                                    <Row gutter={[16, 16]}>
                                                                        <Col xs={24} sm={10}>
                                                                            <RouteNameField
                                                                                form={stationForm}
                                                                                name={name}
                                                                                stationsData={stationsData}
                                                                            />
                                                                        </Col>


                                                                        <Col xs={24} sm={10}>
                                                                            <Form.Item
                                                                                name={[name, 'route', 'number_of_km']}
                                                                                label={t("manage_lines.labels.numberOfKm")}
                                                                                rules={[{
                                                                                    required: true,
                                                                                    message: t("manage_lines.errors.numberOfKmRequired")
                                                                                }]}
                                                                            >
                                                                                <InputNumber
                                                                                    min={0}
                                                                                    className="w-full"
                                                                                    placeholder="km"
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                    </Row>
                                                                </div>
                                                            </>
                                                        )}

                                                        {/* Section Station */}
                                                        <Row gutter={[16, 16]} align="stretch">
                                                            <Col xs={24} sm={10}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "id_station"]}
                                                                    rules={[{ required: true, message: t("manage_lines.errors.stationRequired") }]}
                                                                >
                                                                    <Select
                                                                        placeholder={t("manage_lines.placeholders.selectStation")}
                                                                        options={stationsData.map((type: any) => ({
                                                                            label: type[`nom_${currentLang}`],
                                                                            value: type.id,
                                                                        }))}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={4}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "position"]}
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_lines.errors.positionRequired")
                                                                    }]}
                                                                >
                                                                    <InputNumber
                                                                        min={1}
                                                                        className="w-full"
                                                                        disabled
                                                                        value={index + 1}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, "type"]}
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_lines.errors.typeRequired")
                                                                    }]}
                                                                >
                                                                    <Select
                                                                        placeholder={t("manage_lines.placeholders.selectStationType")}
                                                                        options={[
                                                                            { label: t("manage_stations.stationTypes.intermediate"), value: "INTER" },
                                                                            { label: t("manage_stations.stationTypes.terminus"), value: "TERMINUS" },
                                                                            { label: t("manage_stations.stationTypes.hidden"), value: "HIDDEN" }
                                                                        ]}
                                                                    />
                                                                </Form.Item>
                                                            </Col>

                                                            <Col xs={4} sm={2}>
                                                                <MinusCircle
                                                                    className="cursor-pointer mt-1"
                                                                    onClick={() => remove(name)}
                                                                    style={{color: "var(--primary-color)"}}
                                                                />
                                                            </Col>

                                                            {(index === 0 || index === fields.length - 1) && (
                                                                <Col className="mb-4" xs={24}>
                                                                    <Spin spinning={loading}>
                                                                            {seasonsData && seasonsData.length > 0 ? (
                                                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                                                    {
                                                                                        seasonsData?.map((season: any) => (
                                                                                            <Card
                                                                                                key={season.id}
                                                                                                title={
                                                                                                    <div className="flex justify-between items-center">
                                                                                                        <span>{season[`nom_${currentLang}`]}</span>
                                                                                                    </div>
                                                                                                }
                                                                                                extra={
                                                                                                    <small className="text-gray-500">
                                                                                                        {moment(season.start_date).format("DD/MM/YYYY")} - {moment(season.end_date).format("DD/MM/YYYY")}
                                                                                                    </small>
                                                                                                }
                                                                                            >
                                                                                                <Form.List
                                                                                                    name={[name, "seasons", season.id, "departure_times"]}
                                                                                                    rules={[
                                                                                                        {
                                                                                                            validator: async (_, times) => {
                                                                                                                if (times && times.some((time: any) => !time)) {
                                                                                                                    return Promise.reject(new Error(t("manage_lines.errors.invalidTime")));
                                                                                                                }
                                                                                                            },
                                                                                                        },
                                                                                                    ]}
                                                                                                >
                                                                                                    {(timeFields, {
                                                                                                        add: addTime,
                                                                                                        remove: removeTime
                                                                                                    }) => (
                                                                                                        <>
                                                                                                            {timeFields.map(({
                                                                                                                                 key: timeKey,
                                                                                                                                 name: timeName
                                                                                                                             }) => (
                                                                                                                <Row key={timeKey} className="mb-2">
                                                                                                                    <Col span={20}>
                                                                                                                        <Form.Item
                                                                                                                            name={timeName}
                                                                                                                            rules={[{
                                                                                                                                required: true,
                                                                                                                                message: t("manage_lines.errors.departureTimeRequired")
                                                                                                                            }]}
                                                                                                                        >
                                                                                                                            <TimePicker
                                                                                                                                format="HH:mm"
                                                                                                                                minuteStep={5}
                                                                                                                                className="w-full"
                                                                                                                            />
                                                                                                                        </Form.Item>
                                                                                                                    </Col>
                                                                                                                    <Col span={4}>
                                                                                                                        <MinusCircle
                                                                                                                            width={20}
                                                                                                                            className="mt-1 mx-1 cursor-pointer"
                                                                                                                            onClick={() => removeTime(timeName)}
                                                                                                                        />
                                                                                                                    </Col>
                                                                                                                </Row>
                                                                                                            ))}
                                                                                                            <Button
                                                                                                                type="dashed"
                                                                                                                onClick={() => addTime(moment().startOf('hour'))}
                                                                                                                className="text-xs w-full"
                                                                                                                icon={<PlusOutlined />}
                                                                                                            >
                                                                                                                {t("manage_lines.addDepartureTime")}
                                                                                                            </Button>
                                                                                                        </>
                                                                                                    )}
                                                                                                </Form.List>
                                                                                            </Card>
                                                                                        ))
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <div className="flex justify-center items-center w-full">
                                                                                    <Empty
                                                                                        description={
                                                                                            <Typography.Text type="secondary">
                                                                                                {t("manage_lines.noSeasons")}
                                                                                            </Typography.Text>
                                                                                        }
                                                                                        className="py-8"
                                                                                    />
                                                                                </div>
                                                                            )}
                                                                    </Spin>
                                                                </Col>
                                                            )}
                                                        </Row>
                                                    </div>
                                                );
                                            })}

                                            <Button
                                                type="dashed"
                                                onClick={() => {
                                                    const currentStations = stationForm.getFieldValue("stations") || [];
                                                    if (currentStations.length > 0) {
                                                        const previousStation = currentStations[currentStations.length - 1];
                                                        if (currentStations.length > 1 && previousStation.departure_time && previousStation.departure_time.length > 0) {
                                                            previousStation.departure_time = [];
                                                            stationForm.setFieldsValue({ stations: currentStations });
                                                        }
                                                    }
                                                    add({
                                                        position: currentStations.length + 1,
                                                        departure_time: [moment().startOf('hour')],
                                                        ...(currentStations.length > 0 && {
                                                            route: {
                                                                is_regular: false,
                                                                status: false,
                                                            },
                                                        }),
                                                    });
                                                }}
                                                block
                                            >
                                                {t("manage_lines.addStation")}
                                            </Button>
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </div>
    );
};

export default LineStationsManager;
