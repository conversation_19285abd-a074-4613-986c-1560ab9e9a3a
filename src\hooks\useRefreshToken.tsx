import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { refreshToken } from "../features/auth/authSlice";
import {jwtDecode} from "jwt-decode";

const TOKEN_STORAGE_KEY = "TOKEN";

const useRefreshToken = () => {
    const dispatch = useDispatch<any>();

    const isTokenExpiringSoon = (token: string) => {
        try {
            const decoded: any = jwtDecode(token);
            // Temps actuel en secondes
            const currentTime = Math.floor(Date.now() / 1000);
            const timeLeft = decoded.exp - currentTime;
            // Expire dans 5 minutes ou moins
            return timeLeft > 0 && timeLeft <= 5 * 60;
        } catch (error) {
            // Si le token est invalide, ne pas le rafraîchir
            return false;
        }
    };

    useEffect(() => {
        const refreshInterval = setInterval(async () => {
            const oldToken = localStorage.getItem(TOKEN_STORAGE_KEY);

            // Vérifier les conditions avant de rafraîchir
            if (oldToken && isTokenExpiringSoon(oldToken)) {
                console.log("🔄 Rafraîchissement du token...");

                try {
                    const action = await dispatch(refreshToken());
                    const newToken = action.payload;

                    if (newToken) {
                        localStorage.setItem(TOKEN_STORAGE_KEY, newToken);
                        console.log("✅ Token mis à jour :", newToken);
                    }
                } catch (error) {
                    console.error("❌ Erreur lors du rafraîchissement du token :", error);
                }
            } else {
                console.log("⏳ Token encore valide ou inexistant, pas de refresh.");
            }
        }, 1 * 60 * 1000); // Vérifie toutes les 5 minutes

        return () => clearInterval(refreshInterval);
    }, [dispatch]);

    return null;
};

export default useRefreshToken;
