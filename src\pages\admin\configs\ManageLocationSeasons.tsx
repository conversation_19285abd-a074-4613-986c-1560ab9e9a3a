import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    DatePicker,
    Switch,
    Tooltip
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined, CalendarOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import dayjs from 'dayjs';
import {
    getLocationSeasons,
    storeLocationSeason,
    updateLocationSeason,
    deleteLocationSeason
} from "../../../features/admin/locationSeasonSlice";

function ManageLocationSeasons() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingLocationSeason, setEditingLocationSeason] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL LOCATION SEASONS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetLocationSeasons = (params: any, sort: any, filter: any) => {
        return dispatch(getLocationSeasons({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t(`manage_locationSeasons.labels.name`),
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_locationSeasons.labels.startDate")}`,
            dataIndex: "start_date",
            valueType: "date",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.start_date,
        },
        {
            title: `${t("manage_locationSeasons.labels.endDate")}`,
            dataIndex: "end_date",
            valueType: "date",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.end_date,
        },
        {
            title: `${t("manage_locationSeasons.labels.status")}`,
            dataIndex: "status",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <Switch
                    checked={record.status}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": { 
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": { 
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_locationSeasons.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_locationSeasons.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_locationSeasons.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_locationSeasons.yes")}
                        cancelText={t("manage_locationSeasons.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/admin-dashboard">{t("auth_sidebar.categories.busLocations")}</Link>,
        },
        {
            title: t("manage_locationSeasons.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        const formData = {
            ...record,
            start_date: record.start_date ? dayjs(record.start_date) : null,
            end_date: record.end_date ? dayjs(record.end_date) : null,
        };
        setEditingLocationSeason(record);
        form.setFieldsValue(formData);
        setViewMode(true);
        setModalVisible(true);
    };
    
    const handleEdit = (record: any) => {
        const formData = {
            ...record,
            start_date: record.start_date ? dayjs(record.start_date) : null,
            end_date: record.end_date ? dayjs(record.end_date) : null,
        };
        setEditingLocationSeason(record);
        form.setFieldsValue(formData);
        setViewMode(false);
        setModalVisible(true);
    };
    
    const handleAdd = () => {
        setEditingLocationSeason(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE LOCATION SEASON
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        
        const formattedValues = {
            ...values,
            start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : null,
            end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : null,
        };
        
        const payload = editingLocationSeason 
            ? { id: editingLocationSeason.id, ...formattedValues } 
            : formattedValues;
            
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingLocationSeason) {
                await dispatch(updateLocationSeason(payload)).unwrap();
            } else {
                await dispatch(storeLocationSeason(formattedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_locationSeasons.confirmAction"),
            content: editingLocationSeason
                ? t("manage_locationSeasons.confirmUpdate")
                : t("manage_locationSeasons.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE LOCATION SEASON
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteLocationSeason(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
     |  - HANDLE RESET
     |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    // Validate that end date is after start date
    const validateEndDate = (_: any, value: any) => {
        const startDate = form.getFieldValue('start_date');
        if (startDate && value && value.isBefore(startDate)) {
            return Promise.reject(new Error(t("manage_locationSeasons.errors.endDateAfterStartDate")));
        }
        return Promise.resolve();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_locationSeasons.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetLocationSeasons(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_locationSeasons.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                open={modalVisible}
                title={
                    viewMode
                        ? t("manage_locationSeasons.details")
                        : editingLocationSeason
                            ? t("manage_locationSeasons.edit")
                            : t("manage_locationSeasons.add")
                }
                onCancel={handleReset}
                footer={
                    viewMode
                        ? [
                            <Button key="close" onClick={handleReset}>
                                {t("common.close")}
                            </Button>,
                        ]
                        : [
                            <Button key="cancel" onClick={handleReset}>
                                {t("common.cancel")}
                            </Button>,
                            <Button
                                key="submit"
                                type="primary"
                                loading={loading}
                                onClick={() => form.submit()}
                            >
                                {t("common.save")}
                            </Button>,
                        ]
                }
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={viewMode}
                >    
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_locationSeasons.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_locationSeasons.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_locationSeasons.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_locationSeasons.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_locationSeasons.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_locationSeasons.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    
                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.startDate")}
                                name="start_date"
                                rules={[{ required: true, message: t("manage_locationSeasons.errors.startDateRequired") }]}
                            >
                                <DatePicker 
                                    style={{ width: '100%' }} 
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.endDate")}
                                name="end_date"
                                rules={[
                                    { required: true, message: t("manage_locationSeasons.errors.endDateRequired") },
                                    { validator: validateEndDate }
                                ]}
                            >
                                <DatePicker 
                                    style={{ width: '100%' }} 
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_locationSeasons.labels.status")}
                                name="status"
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Switch disabled={viewMode} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageLocationSeasons;
