import {useEffect, useRef, useState} from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,

    Breadcrumb,
    Row,
    Col, 
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {getGovernorateAll} from "../../../features/admin/governorateSlice.ts";
import {useDispatch} from "react-redux";
import {getDelegationsByGovernorate} from "../../../features/admin/delegationSlice.ts";
import {getEstablishmentTypeAll} from "../../../features/admin/establishmentTypeSlice.ts";
import {deleteEstablishment, getEstablishments, storeEstablishment, updateEstablishment} from "../../../features/admin/establishmentSlice.ts";
import {toast} from "react-toastify";
import { useSelector } from "react-redux";

function ManageEstablishments() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch = useDispatch<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingEstablishment, setEditingEstablishment] = useState<any>(null);
    const [form] = Form.useForm();

    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
    const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const governorates = useSelector((state: any) => state.governorate.items.data);
    const establishmentTypes = useSelector((state: any) => state.establishmentType.items.data);



    {/*|--------------------------------------------------------------------------
    | FETCH ESTABLISHMENTS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetEstablishments:any = (params: any, sort: any, filter: any) => {
        return dispatch(getEstablishments({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
                toast.error(t("common.errors.unexpected"));
            });
    }

     const fetchStoreData = async () => { 
        if(!governorates?.length){
            await dispatch(getGovernorateAll()).unwrap()
        }
        if (!establishmentTypes?.length) {
            await dispatch(getEstablishmentTypeAll()).unwrap();
        }
    }

    useEffect(() => {
        fetchStoreData();
    }, []);

    
    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns:any = [
        {
            title: `${t("manage_establishment.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`] || '-',
        },
        {
            title: `${t("manage_establishment.labels.abbreviation")}`,
            dataIndex: "abbreviation",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.abbreviation || '-',
        },
        {
            title: `${t("manage_establishment.labels.governorate")}`,
            dataIndex: "id_governorate",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.delegation?.governorate?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    placeholder={t("manage_establishment.filters.governorate")}
                    options={governorates?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    onChange={handleGovernorateChange}
                />
            ),
        },
        {
            title: `${t("manage_establishment.labels.delegation")}`,
            dataIndex: "id_delegation",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.delegation?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    loading={isDelegationsLoading}
                    placeholder={t("manage_establishment.filters.delegation")}
                    options={filteredDelegations?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                    disabled={!selectedFilterGovernorate}
                    className="w-full"
                    notFoundContent={
                        isDelegationsLoading ? (
                            <div className="flex items-center justify-center py-2">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                            </div>
                        ) : (
                            <div className="text-center py-2 text-gray-500">
                                {!selectedFilterGovernorate 
                                    ? t("manage_establishment.selectGovernorate") 
                                    : t("common.noData")}
                            </div>
                        )
                    }
                />
            ),
        },
        {
            title: `${t("manage_establishment.labels.establishmentType")}`,
            dataIndex: "id_type_establishment",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record?.type_establishment?.[`nom_${currentLang}`] || '-',
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    placeholder={t("manage_establishment.filters.establishmentType")}
                    options={establishmentTypes?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: `${t("manage_establishment.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_establishment.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_establishment.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_establishment.yes")}
                        cancelText={t("manage_establishment.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.education")}</Link>,
        },
        {
            title: t("manage_establishment.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleAdd = () => {
        setEditingEstablishment(null);
        setViewMode(false);
        form.resetFields();
        setModalVisible(true);
    };
    const handleEdit = async (record: any) => {
        setEditingEstablishment(record);
        if (record.delegation) {
            await handleGovernorateChange(record.delegation?.id_governorate);
        }
        form.setFieldsValue({
            ...record,
            id_governorate: record.delegation?.id_governorate || null,
            id_delegation: record.delegation?.id || null,
            id_type_establishment: record.type_establishment?.id,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleView = (record: any) => {
        setEditingEstablishment(record);
        form.setFieldsValue({
            ...record,
            id_governorate: record.delegation?.governorate?.id,
            id_delegation: record.delegation?.id,
            id_type_establishment: record.type_establishment?.id,
        });
        handleGovernorateChange(record.governorate?.id);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleGovernorateChange: any = async (governorateId: number | null) => {
        form.setFieldsValue({ id_delegation: null });
        setSelectedFilterGovernorate(governorateId);
        setIsDelegationsLoading(true);
        setFilteredDelegations([]);
        if (!governorateId) {
            setFilteredDelegations([]);
            setIsDelegationsLoading(false);
            return;
        }
        try {
            const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
            setFilteredDelegations(response.data || []);
        } catch (error) {
            console.error('Error fetching delegations:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setIsDelegationsLoading(false);
        }
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE ESTABLISHMENT
    |-------------------------------------------------------------------------- */
    const handleFormSubmit:any = async (values: any) => {
        setLoading(true);
        const payload = editingEstablishment ? { id: editingEstablishment.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingEstablishment) {
                await dispatch(updateEstablishment(payload)).unwrap();
            } else {
                await dispatch(storeEstablishment(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            console.log(error);
            
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit:any = (values: any) => {
        const modal:any = Modal.confirm({
            title: t("manage_establishment.confirmAction"),
            content: editingEstablishment
                ? t("manage_establishment.confirmUpdate")
                : t("manage_establishment.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE DELETE
    |-------------------------------------------------------------------------- */
    const handleDelete:any = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteEstablishment(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };


    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        setViewMode(false)
        form.resetFields();
    }


    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_establishment.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetEstablishments(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_establishment.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                title={
                    viewMode
                        ? t("manage_establishment.view")
                        : editingEstablishment
                            ? t("manage_establishment.edit")
                            : t("manage_establishment.add")
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={
                    viewMode ? [
                        <Button key="close" onClick={() => setModalVisible(false)}>
                            {t("common.close")}
                        </Button>
                    ] : [
                        <Button key="cancel" onClick={() => setModalVisible(false)}>
                            {t("common.cancel")}
                        </Button>,
                        <Button key="submit" type="primary" onClick={form.submit}>
                            {t("common.save")}
                        </Button>
                    ]
                }
                width={900}
            >
                <Form
                    form={form}
                    className="form-inputs"
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={viewMode}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="abbreviation"
                                label={t("manage_establishment.labels.abbreviation")}
                                rules={[{ required: true, message: t("manage_establishment.errors.abbreviationRequired") }]}
                            >
                                <Input placeholder={t("manage_establishment.placeholders.abbreviation")} />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_type_establishment"
                                label={t("manage_establishment.labels.establishmentType")}
                                rules={[{ required: true, message: t("manage_establishment.errors.establishmentTypeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_establishment.placeholders.establishmentType")}
                                    options={establishmentTypes?.map((type: any) => ({
                                        label: type[`nom_${currentLang}`],
                                        value: type.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_governorate"
                                label={t("manage_establishment.labels.governorate")}
                                rules={[{ required: true, message: t("manage_establishment.errors.governorateRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_establishment.placeholders.governorate")}
                                    onChange={handleGovernorateChange}
                                    options={governorates?.map((gov: any) => ({
                                        label: gov[`nom_${currentLang}`],
                                        value: gov.id,
                                    }))}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                name="id_delegation"
                                label={t("manage_establishment.labels.delegation")}
                                rules={[{ required: true, message: t("manage_establishment.errors.delegationRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_establishment.placeholders.delegation")}
                                    options={filteredDelegations.map((del: any) => ({
                                        label: del[`nom_${currentLang}`],
                                        value: del.id,
                                    }))}
                                    notFoundContent={
                                        isDelegationsLoading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!selectedFilterGovernorate
                                                    ? t("manage_establishment.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                     <Row gutter={16}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                name="cnss_etab"
                                label={t("manage_establishment.labels.cnssEtab")}
                                rules={[{ required: true, message: t("manage_establishment.errors.cnssEtabRequired") }]}
                            >
                                <Input placeholder={t("manage_establishment.placeholders.cnssEtab")} />
                            </Form.Item>
                        </Col>
                    </Row>

                </Form>
            </Modal>
        </>
    );
}

export default ManageEstablishments;
