import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    message,
    Breadcrumb,
    Row,
    Col,
    Select,
    Tag,
    Switch,
    DatePicker,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import { useDispatch } from "react-redux";
import { deleteDiscount, getDiscounts, storeDiscount, updateDiscount } from "../../../features/admin/discountSlice.ts";
import { toast } from "react-toastify";
import { getPeriodicityAll } from "../../../features/admin/periodicitySlice.ts";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import dayjs from 'dayjs';
import moment from 'moment';



function ManageDiscounts() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch = useDispatch();

    const [periodicities, setPeriodicities] = useState([]);
    const [subsTypes, setSubsTypes] = useState([]);

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingDiscount, setEditingDiscount] = useState<any>(null);
    const [selectedSubsType, setSelectedSubsType] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);


    /*|--------------------------------------------------------------------------
    | FETCH ALL card types WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetDiscounts = (params:any,sort:any,filter:any) =>
        dispatch(getDiscounts({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

        useEffect(() => {
            const fetchData = async () => {
                try {
                    const [periodicityResponse, subsTypesResponse] = await Promise.all([
                        dispatch(getPeriodicityAll()).unwrap(),
                        dispatch(getAbnTypesAll()).unwrap(),
                    ]);

                    setPeriodicities(periodicityResponse?.data || []);
                    setSubsTypes(subsTypesResponse?.data || []);
                } catch (error) {
                    console.error('Error fetching data:', error);
                    toast.error(t("common.errors.unexpected"));
                }
            };

            fetchData();
        }, []);


    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_discounts.labels.name")}`,
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_discounts.labels.discountPercentage")}`,
            dataIndex: "percentage",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.percentage,
        },
        {
            title: `${t("manage_discounts.labels.subsType")}`,
            dataIndex: "id_subs_type",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data.subs_type ? (
                    <Tag color={data.subs_type.color || "default"}>
                        {data.subs_type[`nom_${currentLang}`]}
                    </Tag>
                ) : (
                    "-"
                );
            },
            renderFormItem: () => (
                <Select
                    showSearch
                    filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    allowClear
                    placeholder={t("manage_discounts.filters.subsType")}
                    options={subsTypes?.map((el:any) => ({
                        label: el[`nom_${currentLang}`] || el.name || '-',
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: `${t("manage_discounts.labels.abnPeriod")}`,
            dataIndex: "periodicities",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data.periodicities.map((period: any) => period[`nom_${currentLang}`]).join(", "),
        },
        {
            title: `${t("manage_discounts.labels.is_stagiaire")}`,
            dataIndex: "is_stagiaire",
            valueType: "select",
            responsive: ["xs", "sm", "md", "lg"],
            valueEnum: {
                true: { text: t("common.yes"), status: "Success" },
                false: { text: t("common.no"), status: "Default" },
            },
            render: (_: any, data: any) => (
                <Switch
                    checked={data.is_stagiaire}
                    disabled
                    size="small"
                />
            ),
        },
        {
            title: `${t("manage_discounts.labels.specialClient")}`,
            dataIndex: "special_client",
            valueType: "select",
            responsive: ["xs", "sm", "md", "lg"],
            valueEnum: {
                "UNIVERSITAIRE": { text: t("manage_newSubs.labels.universitaire"), status: "Success" },
                "SCOLAIRE": { text: t("manage_newSubs.labels.scolaire"), status: "Success" },
                null: { text: t("manage_newSubs.labels.default"), status: "Default" },
            },
            render: (_: any, data: any) => (
                <Tag color={data.special_client ? "success" : "default"}>
                    {data.special_client ? data.special_client : t("manage_newSubs.labels.default")}
                </Tag>
            ),
        },
        {
            title: `${t("manage_discounts.labels.date_start")}`,
            dataIndex: "date_start",
            valueType: "date",
            width: 120,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.date_start ? dayjs(record.date_start).format('MM-DD') : '-',
        },
        {
            title: `${t("manage_discounts.labels.date_end")}`,
            dataIndex: "date_end",
            valueType: "date",
            width: 120,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.date_end ? dayjs(record.date_end).format('MM-DD') : '-',
        },
        {
            title: `${t("manage_discounts.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_discounts.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_discounts.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_discounts.yes")}
                        cancelText={t("manage_discounts.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_discounts.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingDiscount(record);
        setSelectedSubsType(record.subs_type);
        form.setFieldsValue({
            ...record,
            id_periodicities: record.periodicities.map((p: any) => p.id),
            is_stagiaire: record.is_stagiaire,
            id_subs_type: record.subs_type?.id,
            date_start: record.date_start ? dayjs(record.date_start) : null,
            date_end: record.date_end ? dayjs(record.date_end) : null,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingDiscount(record);
        setSelectedSubsType(record.subs_type);
        form.setFieldsValue({
            ...record,
            id_periodicities: record.periodicities.map((p: any) => p.id),
            is_stagiaire: record.is_stagiaire,
            id_subs_type: record.subs_type?.id,
            date_start: record.date_start ? dayjs(record.date_start) : null,
            date_end: record.date_end ? dayjs(record.date_end) : null,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingDiscount(null);
        setSelectedSubsType(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE DISCOUNT
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
           setLoading(true);

           // Format date fields
           const formattedValues = {
               ...values,
               date_start: dayjs(values.date_start).format('YYYY-MM-DD'),
               date_end: dayjs(values.date_end).format('YYYY-MM-DD'),
           };

           const payload = editingDiscount ? { id: editingDiscount.id, ...formattedValues } : formattedValues;
           const toastId = toast.loading(t("messages.loading"), {
               position: 'top-center',
           });
           try {
               if (editingDiscount) {
                   await dispatch(updateDiscount(payload)).unwrap();
               } else {
                   await dispatch(storeDiscount(values)).unwrap();
               }
               toast.update(toastId, {
                   render: t("messages.success"),
                   type: "success",
                   isLoading: false,
                   autoClose: 3000
               });
               actionRef.current?.reload();
               handleReset();
           } catch (error: any) {
                const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                    name: field,
                    errors: [error.errors[field][0]],
                }));
                form.setFields(fieldErrors);
                toast.update(toastId, {
                    render: t("messages.error"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
           } finally {
               setLoading(false);
           }
       };

       const confirmSubmit = (values: any) => {
           const modal = Modal.confirm({
               title: t("manage_discounts.confirmAction"),
               content: editingDiscount
                   ? t("manage_discounts.confirmUpdate")
                   : t("manage_discounts.confirmAdd"),
               okText: t("common.yes"),
               cancelText: t("common.no"),
               onOk: async () => {
                   modal.destroy();
                   await handleFormSubmit(values);
               },
               centered: true,
           });
       };

     /*|--------------------------------------------------------------------------
       |  - DELETE DISCOUNT
       |-------------------------------------------------------------------------- */
       const handleDelete = async (id: number) => {
           const toastId = toast.loading(t("messages.loading"), {
               position: 'top-center',
           });
           try {
               await dispatch(deleteDiscount(id)).unwrap()
               toast.update(toastId, {
                   render: t("messages.success"),
                   type: "success",
                   isLoading: false,
                   autoClose: 3000
               });
               actionRef.current?.reload();
           } catch (error: any) {
               toast.update(toastId, {
                   render: t("messages.error"),
                   type: "error",
                   isLoading: false,
                   autoClose: 3000
               });
           }
       };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        setViewMode(false)
        form.resetFields();
        setLoading(false)
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_discounts.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetDiscounts(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_discounts.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_discounts.details")
                        : editingDiscount
                            ? t("manage_discounts.edit")
                            : t("manage_discounts.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_discounts.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                            label={t("manage_discounts.labels.subsType")}
                            name="id_subs_type"
                            rules={[{ required: true, message: t("manage_users.errors.subsTypeRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_discounts.placeholders.subsType")}
                                    onChange={(value) => {
                                        const selectedType = subsTypes.find((type: any) => type.id === value);
                                        setSelectedSubsType(selectedType);
                                    }}
                                >
                                    {subsTypes.map((el: any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_discounts.labels.abnPeriod")}
                                name="id_periodicities"
                                rules={[{ required: true, message: t("manage_discounts.errors.abnPeriodRequired") }]}
                            >
                                <Select
                                    mode="multiple"
                                    disabled={viewMode}
                                    placeholder={t("manage_discounts.placeholders.abnPeriod")}
                                >
                                    {periodicities.map((el: any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_discounts.labels.discountPercentage")}
                                name="percentage"
                                rules={[{ required: true, message: t("manage_discounts.errors.discountPercentageRequired") }]}
                            >
                                <Input disabled={viewMode}  placeholder={t("manage_discounts.placeholders.discountPercentage")} min={0} max={100} style={{ width: "100%" }} />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_discounts.labels.date_start")}
                                name="date_start"
                                rules={[{ required: true, message: t("manage_discounts.errors.dateStartRequired") }]}
                            >
                                <DatePicker
                                    className="w-full"
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                    placeholder={t("manage_discounts.placeholders.date_start")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_discounts.labels.date_end")}
                                name="date_end"
                                rules={[{ required: true, message: t("manage_discounts.errors.dateEndRequired") }]}
                            >
                                <DatePicker
                                    className="w-full"
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                    placeholder={t("manage_discounts.placeholders.date_end")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16,16]}>
                        {selectedSubsType && !selectedSubsType.is_student && !selectedSubsType.is_impersonal && !selectedSubsType.is_conventional && (
                            <Col xs={24} sm={12}>
                                <Form.Item
                                    name="special_client"
                                    label={t("manage_newSubs.labels.specialClient")}
                                    initialValue={null}
                                >
                                    <Select
                                        disabled={viewMode}
                                        placeholder={t("manage_newSubs.placeholders.specialClient")}
                                    >
                                        <Select.Option value={null}>{t("manage_newSubs.labels.default")}</Select.Option>
                                        <Select.Option value={"UNIVERSITAIRE"}>{t("manage_newSubs.labels.universitaire")}</Select.Option>
                                        <Select.Option value={"SCOLAIRE"}>{t("manage_newSubs.labels.scolaire")}</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                        )}
                        <Col xs={24} sm={selectedSubsType && !selectedSubsType.is_student && !selectedSubsType.is_impersonal && !selectedSubsType.is_conventional ? 12 : 24}>
                            <Form.Item
                                label={t("manage_discounts.labels.is_stagiaire")}
                                name="is_stagiaire"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch disabled={viewMode} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageDiscounts;
