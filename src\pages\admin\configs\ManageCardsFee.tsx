import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    message,
    Breadcrumb,
    Row,
    Col, Select, Tag,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {AbnTypesData, cardFees} from "../../../data/fakeData.ts";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { deleteCardFee, getCardFees, storeCardFee, updateCardFee } from "../../../features/admin/cardFeeSlice.ts";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import { useSelector } from "react-redux";


function ManageCardsFee() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch:any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingCardFee, setEditingCardFee] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const abnTypes = useSelector((state: any) => state.abnType.items.data);

    /*|--------------------------------------------------------------------------
    | FETCH ALL CAMPAIGNS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetCardFees = (params: any, sort: any, filter: any) => {
        return dispatch(
            getCardFees({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            })
            )
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    };

     const fetchStoreData = async () => {
                if(!abnTypes?.length){
                    await dispatch(getAbnTypesAll()).unwrap()
                }
            }
            useEffect(() => {
                fetchStoreData()
            }, []);


    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_cardsFees.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_cardsFees.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_cardsFees.labels.abnType")}`,
            dataIndex: "id_subs_type",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => {
                return record.subs_type ? (
                    <Tag color={record.subs_type.color || "default"}>
                        {record.subs_type[`nom_${currentLang}`]}
                    </Tag>
                ) : (
                    "-"
                );
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_cardsFees.placeholders.abnType"),
                options: abnTypes?.map((abnType: any) => ({
                    label: abnType[`nom_${currentLang}`],
                    value: abnType.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: `${t("manage_cardsFees.labels.amount")}`,
            dataIndex: "amount",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return (
                    <div className="font-semibold">{data.amount}</div>
                )
            },
        },
        {
            title: `${t("manage_cardsFees.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_cardsFees.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_cardsFees.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_cardsFees.yes")}
                        cancelText={t("manage_cardsFees.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];

    const breadcrumbItems:any = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_cardsFees.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView:any = (record: any) => {
        setEditingCardFee(record);
        form.setFieldsValue({
            ...record,
            governorate_id: record.governorate?.id,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit:any = (record: any) => {
        setEditingCardFee(record);
        form.setFieldsValue({
            ...record,
            governorate_id: record.governorate?.id,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd:any = () => {
        setEditingCardFee(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE CARD FEE
    |-------------------------------------------------------------------------- */
     const handleFormSubmit = async (values: any) => {
            setLoading(true);
            const transformedValues = {
                ...values,
                ranges: Object.entries(values.ranges || {})?.map(([cardTypeId, range]:any) => ({
                    cardType: parseInt(cardTypeId),
                    ...range
                }))
            };
    
            const payload = editingCardFee 
                ? { id: editingCardFee.id, ...transformedValues } 
                : transformedValues;
    
            const toastId = toast.loading(t("messages.loading"), {
                position: 'top-center',
            });
            try {
                if (editingCardFee) {
                    await dispatch(updateCardFee(payload)).unwrap();
                } else {
                    await dispatch(storeCardFee(transformedValues)).unwrap();
                }
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
                actionRef.current?.reload();
                handleReset();
            } catch (error: any) {
                setLoading(false);
                const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                    name: field,
                    errors: [error.errors[field][0]],
                }));
                form.setFields(fieldErrors);
                toast.update(toastId, {
                    render: t("messages.error"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
            }
        };
        const confirmSubmit = (values: any) => {
            const modal = Modal.confirm({
                title: t("manage_cardsFees.confirmAction"),
                content: editingCardFee
                    ? t("manage_cardsFees.confirmUpdate")
                    : t("manage_cardsFees.confirmAdd"),
                okText: t("common.yes"),
                cancelText: t("common.no"),
                onOk: async () => {
                    modal.destroy();
                    await handleFormSubmit(values);
                },
                centered: true,
            });
        };
    

    /*|--------------------------------------------------------------------------
    |  - DELETE CARD FEE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            await dispatch(deleteCardFee(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        className="component-modern"
                        headerTitle={t("manage_cardsFees.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetCardFees(
                                params,
                                sort,
                                filter
                            );
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_cardsFees.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_cardsFees.details")
                        : editingCardFee
                            ? t("manage_cardsFees.edit")
                            : t("manage_cardsFees.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_cardsFees.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_discounts.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_discounts.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_discounts.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                   <Row gutter={[16,16]}>
                       <Col xs={24} sm={12}>
                           <Form.Item
                               label={t("manage_cardsFees.labels.abnType")}
                               name="id_subs_type"
                               rules={[{ required: true, message: t("manage_cardsFees.errors.abnTypeRequired") }]}
                           >
                               <Select placeholder={t("manage_cardsFees.placeholders.abnType")} disabled={viewMode}>
                                   {abnTypes?.map((el:any) => (
                                       <Select.Option key={el.id} value={el.id}>
                                           {el[`nom_${currentLang}`]}
                                       </Select.Option>
                                   ))}
                               </Select>
                           </Form.Item>
                       </Col>
                       <Col xs={24} sm={12}>
                           <Form.Item
                               label={t("manage_cardsFees.labels.amount")}
                               name="amount"
                               rules={[{ required: true, message: t("manage_cardsFees.errors.amountRequired") }]}
                           >
                               <Input
                                   type="number"
                                   placeholder={t("manage_cardsFees.placeholders.amount")}
                                   disabled={viewMode}
                               />
                           </Form.Item>
                       </Col>
                   </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageCardsFee;