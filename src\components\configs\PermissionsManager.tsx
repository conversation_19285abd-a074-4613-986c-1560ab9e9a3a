import {<PERSON>, Button, Tree, Spin} from "antd";
import { ArrowLeftCircle, ArrowRightCircle } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {toast, ToastContainer} from "react-toastify";
import {
    assignPermissions,
    getAssignedUnassignedPermissionsRole,
    removePermissions
} from "../../features/auth/roleSlice.ts";

interface Permission {
    id: number;
    name: string;
    category_name: string;
}

interface TreeNode {
    title: string;
    key: string;
    children?: TreeNode[];
}

function PermissionsManager({ roleId }: { roleId: number }) {
    const { t, i18n } = useTranslation();
    const dispatch = useDispatch();
    const [loading, setLoading] = useState<boolean>(false);

    const [unassignedPermissions, setUnassignedPermissions] = useState<Permission[]>([]);
    const [assignedPermissions, setAssignedPermissions] = useState<Permission[]>([]);
    const [selectedUnassignedKeys, setSelectedUnassignedKeys] = useState<string[]>([]);
    const [selectedAssignedKeys, setSelectedAssignedKeys] = useState<string[]>([]);

    useEffect(() => {
        fetchAssignedUnassignedPermissionsRole();
    }, [dispatch, roleId]);


    {/*|--------------------------------------------------------------------------
    | FETCH ALL ASSIGNED & UNASSIGNED PERMISSION FOR THE ROLE
    |-------------------------------------------------------------------------- */}
    const fetchAssignedUnassignedPermissionsRole = async () => {
        setLoading(true);
        try {
            const res = await dispatch(getAssignedUnassignedPermissionsRole(roleId));
            const { unassigned, assigned } = res.payload;
            setUnassignedPermissions(unassigned);
            setAssignedPermissions(assigned);
        } catch (error) {
            console.log(error);
        } finally {
            handleReset()
        }
    };

    {/*|--------------------------------------------------------------------------
    | GROUP PERMISSIONS BY CATEGORY
    |-------------------------------------------------------------------------- */}
    const groupPermissionsByCategory = (permissions: Permission[]): TreeNode[] => {
        const categories: { [key: string]: TreeNode } = {};
        permissions.forEach(({ id, name, category_name }) => {
            
            
            if (!categories[category_name]) {
                categories[category_name] = { title: t("permissions." +category_name), key: category_name, children: [] };
            }
            categories[category_name].children?.push({ title: t("permissions." + name), key: id.toString() });
        });
        return Object.values(categories);
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE ASSIGN PERMISSION TO A ROLE
    |-------------------------------------------------------------------------- */}
    const handleAssign = async () => {
        if (!selectedUnassignedKeys.length) return;
        const permissionIds = selectedUnassignedKeys.map(Number);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        setLoading(true);
        try {
            await dispatch(assignPermissions({ roleId, permissionIds })).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            setAssignedPermissions([...assignedPermissions, ...unassignedPermissions.filter(p => permissionIds.includes(p.id))]);
            setUnassignedPermissions(unassignedPermissions.filter(p => !permissionIds.includes(p.id)));
            setSelectedUnassignedKeys([]);
        } catch (error) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            handleReset()
        }
    };

    {/*|--------------------------------------------------------------------------
    | HANDLE REMOVE PERMISSION TO A ROLE
    |-------------------------------------------------------------------------- */}
    const handleRemove = async () => {
        if (!selectedAssignedKeys.length) return;
        const permissionIds = selectedAssignedKeys.map(Number);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        setLoading(true);
        try {
            await dispatch(removePermissions({ roleId, permissionIds })).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            setUnassignedPermissions([...unassignedPermissions, ...assignedPermissions.filter(p => permissionIds.includes(p.id))]);
            setAssignedPermissions(assignedPermissions.filter(p => !permissionIds.includes(p.id)));
            setSelectedAssignedKeys([]);
        } catch (error) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }finally {
            handleReset();
        }
    };

    {/*|--------------------------------------------------------------------------
    | EXCLUDE CATEGORIES IDs FROM PERMISSIONS LISTS
    |-------------------------------------------------------------------------- */}
    const extractLeafKeys = (checkedNodes: any[]) => {
        return checkedNodes
            .filter((node) => !node.children || node.children.length === 0)
            .map((node) => node.key.toString());
    };

    {/*|--------------------------------------------------------------------------
    | RESET FUNCTION
    |-------------------------------------------------------------------------- */}
    const handleReset = () => {
        setLoading(false)
    }

    return (
        <div className="flex flex-wrap gap-5 justify-center">
            <ToastContainer />
            <Card title={t("manage_rolePerm.permissions.unassigned_permissions")} className="w-[400px] shadow-sm hover:shadow-none">
                <Spin spinning={loading}>
                <Tree
                    checkable
                    treeData={groupPermissionsByCategory(unassignedPermissions)}
                    onCheck={(_, { checkedNodes }) => setSelectedUnassignedKeys(extractLeafKeys(checkedNodes))}
                />
                </Spin>
            </Card>

            <div className="flex flex-col gap-4 items-center justify-center">
                <Button onClick={handleAssign} disabled={selectedUnassignedKeys.length === 0 || loading} className="btn-add">
                    {i18n.language === "ar" ? <ArrowLeftCircle /> : <ArrowRightCircle />}
                </Button>
                <Button onClick={handleRemove} disabled={selectedAssignedKeys.length === 0 || loading} className="btn-delete">
                    {i18n.language === "ar" ? <ArrowRightCircle /> : <ArrowLeftCircle />}
                </Button>
            </div>

            <Card title={t("manage_rolePerm.permissions.assigned_permissions")} className="w-[400px] shadow-sm hover:shadow-none">
                <Spin spinning={loading}>
                <Tree
                    checkable
                    treeData={groupPermissionsByCategory(assignedPermissions)}
                    onCheck={(_, { checkedNodes }) => setSelectedAssignedKeys(extractLeafKeys(checkedNodes))}
                />
                </Spin>
            </Card>
        </div>
    );
}

export default PermissionsManager;
