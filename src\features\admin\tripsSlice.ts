import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/trips";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getTripsAll: any = createAsyncThunk(
    "getTripsAll",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-all`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const getTripsNotInterAll: any = createAsyncThunk(
    "getTripsNotInterAll",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-not-inter-all`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getTrips: any = createAsyncThunk(
    "getTrips",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, line, station_start, station_end, status, number_of_km } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (line) {
                searchParams.push(`id_line:${line}`);
            }
            if (station_start) {
                searchParams.push(`id_station_start:${station_start}`);
            }
            if (station_end) {
                searchParams.push(`id_station_end:${station_end}`);
            }
            if (status) {
                searchParams.push(`status:${status}`);
            }
            if (number_of_km) {
                searchParams.push(`number_of_km:${number_of_km}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeTrip: any = createAsyncThunk(
    "storeTrip",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateTrip: any = createAsyncThunk(
    "updateTrip",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteTrip: any = createAsyncThunk(
    "deleteTrip",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const tripsSlice = createSlice({
    name: 'trips',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getTripsAll
            .addCase(getTripsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTripsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTripsAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

          // getTripsNotInterAll
            .addCase(getTripsNotInterAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTripsNotInterAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getTripsNotInterAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })


            // getTrips
            .addCase(getTrips.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getTrips.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getTrips.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeTrip
            .addCase(storeTrip.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeTrip.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storeTrip.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateTrip
            .addCase(updateTrip.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateTrip.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateTrip.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteTrip
            .addCase(deleteTrip.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteTrip.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteTrip.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = tripsSlice.actions;
export default tripsSlice.reducer;