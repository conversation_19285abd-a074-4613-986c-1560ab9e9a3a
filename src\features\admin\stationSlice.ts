import { createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/stations';

export const getStationsAll: any = createAsyncThunk(
    "getStationsAll",
    async (_:any,thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getStations: any = createAsyncThunk(
    "getStations",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI: any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, type_station, status, id_governorate, id_delegation } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (type_station) {
                searchParams.push(`type_station:${type_station}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }
            if (id_governorate) {
                searchParams.push(`id_governorate:${id_governorate}`);
            }
            if (id_delegation) {
                searchParams.push(`id_delegation:${id_delegation}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeStation: any = createAsyncThunk(
    "storeStation",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateStation: any = createAsyncThunk(
    "updateStation",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteStation: any = createAsyncThunk(
    "deleteStation",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getStationsBySubscriptionType: any = createAsyncThunk(
    "getStationsBySubscriptionType",
    async (params: { subsTypeId: number, delegationId?: number | null }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscription-type/${params.subsTypeId}`;

            // Add delegationId parameter if provided
            if (params.delegationId) {
                url += `?delegationId=${params.delegationId}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getStationsForTrip: any = createAsyncThunk(
    "getStationsForTrip",
    async (params: { stationId: number, subsTypeId: number, delegationId?: number | null }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/${params.stationId}/connected-stations/${params.subsTypeId}`;

            if (params.delegationId) {
                url += `?delegationId=${params.delegationId}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);