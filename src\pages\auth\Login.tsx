import React, { useEffect, useState } from "react";
import { Form, Input, Button, Card, Select, Space, Spin } from "antd";
import { Loader, Lock, RefreshCcw } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link, Navigate } from "react-router-dom";
import countryOptions from "../../data/countryPhoneCodes.ts";
import { LanguageSelector } from "../../components";
import { useDispatch, useSelector } from "react-redux";
import {
  checkAuthStatus,
  fetchCaptcha,
  login,
} from "../../features/auth/authSlice.ts";
import { toast, ToastContainer } from "react-toastify";
import { assets } from "../../assets/assets.ts";

const Login: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch: any = useDispatch();
  const { loading, captcha, loadingCaptcha } = useSelector(
    (state: any) => state.auth
  );

  const { isAuthenticated } = useSelector((state: any) => state.auth);

  const [identifierType, _] = useState<string>("email");
  const [form] = Form.useForm();

  useEffect(() => {
    dispatch(checkAuthStatus());
  }, [dispatch]);

  useEffect(() => {
    reloadCaptcha();
  }, []);

  if (isAuthenticated) {
    return <Navigate to="/auth/admin-dashboard" />;
  }

  const reloadCaptcha = async () => {
    await dispatch(fetchCaptcha());
  };

  const onFinish = async (values: any) => {
    let identifier;
    if (identifierType === "phone") {
      const countryCode = values.countryCode || "+216";
      identifier = `${countryCode}-${values.identifier}`;
    } else if (identifierType === "cin") {
      identifier = values.identifier;
    } else {
      identifier = values.identifier;
    }
    const credentialsData = {
      identifier,
      password: values.password,
      captcha: values.captcha,
      generatedCaptcha: localStorage.getItem("hashed_captcha_text"),
    };
      await dispatch(login(credentialsData))
        .unwrap()
        .then(() => {
          localStorage.removeItem("hashed_captcha_text");
        })

        .catch((error: any) => {
          if (error.errors) {
            const fieldErrors = Object.entries(error.errors).map(
              ([name, errors]) => ({
                name,
                errors: Array.isArray(errors) ? errors : [errors],
              })
            );
            form.setFields(fieldErrors);
          }   
        });
   
  };

  return (
    <div className="min-h-screen flex">
      <ToastContainer />
      {/*|--------------------------------------------------------------------------
            |  - LOGIN FORM
            |-------------------------------------------------------------------------- */}
      <div
        className="
                    w-full
                    md:w-full
                    flex items-center justify-center
                    bg-center bg-no-repeat bg-contain
                    "
        style={{
          backgroundImage: `url(${assets.bg})`,
        }}
      >
        <Card
          className="w-full max-w-full  md:max-w-2xl lg:max-w-3xl m-5 !drop-shadow-sm py-20 lg:py-8"
          bordered={false}
        >
          {/*|--------------------------------------------------------------------------
                    |  - SELECT LANGUAGES
                    |-------------------------------------------------------------------------- */}
          <div
            className={`absolute top-5 ${
              i18n.language === "ar" ? "right-5" : "left-5"
            } `}
          >
            <LanguageSelector />
          </div>

          <div className="text-center mb-7">
            <h2 className="text-3xl font-bold text-gray-800 mb-2">
              {t("login.welcome")}
            </h2>
            <p className="text-gray-600 flex items-center justify-center gap-2">
              {t("login.subtitle")}
            </p>
          </div>

          <Form
            form={form}
            name="login"
            initialValues={{
              remember: true,
              countryCode: "+216",
            }}
            onFinish={onFinish}
            layout="vertical"
            size="large"
            className="px-2 auth-form"
          >
            <Form.Item
              hasFeedback
              name="identifier"
              rules={[
                { required: true, message: t("login.validation.required") },
                ...(identifierType === "email"
                  ? [
                      {
                        type: "email" as const,
                        message: t("login.validation.email"),
                      },
                    ]
                  : []),
                ...(identifierType === "phone"
                  ? [
                      {
                        pattern: /^[0-9]{8}$/,
                        message: t("login.validation.phone"),
                      },
                    ]
                  : []),
                ...(identifierType === "cin"
                  ? [
                      {
                        pattern: /^[0-9]{8}$/,
                        message: t("login.validation.cin"),
                      },
                    ]
                  : []),
              ]}
            >
              {identifierType === "email" ? (
                <Input
                  type="email"
                  placeholder={t(`login.placeholder.email`)}
                  className="text-sm"
                />
              ) : identifierType === "phone" ? (
                <Space.Compact style={{ width: "100%" }}>
                  <Form.Item hasFeedback={false} name="countryCode" noStyle>
                    <Select
                      disabled={true}
                      suffixIcon={null}
                      virtual={false}
                      className="!w-1/2"
                      style={{ height: "43px" }}
                      options={countryOptions.map((country: any) => ({
                        value: country.value,
                        label: (
                          <div className="flex items-center gap-2">
                            <img
                              src={country.flag}
                              alt="."
                              className="w-5 h-3 rounded-sm"
                            />
                            <span>({country.value})</span>
                          </div>
                        ),
                      }))}
                    />
                  </Form.Item>

                  <Input
                    className="text-sm"
                    maxLength={8}
                    minLength={8}
                    type="number"
                    placeholder={t(`login.placeholder.phone`)}
                  />
                </Space.Compact>
              ) : (
                <Input
                  maxLength={8}
                  minLength={8}
                  type="number"
                  placeholder={t("login.placeholder.cin")}
                  className="text-sm"
                />
              )}
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                {
                  required: true,
                  message:
                    t("login.validation.required") || "Password is required",
                },
              ]}
            >
              <Input.Password
                prefix={<Lock className="text-gray-400" size={18} />}
                placeholder={
                  t("login.placeholder.password") || "Enter your password"
                }
                className="text-sm"
              />
            </Form.Item>

            <Form.Item
              name="captcha"
              className="!mb-3"
              rules={[
                {
                  required: true,
                  message: t("login.validation.required"),
                },
              ]}
            >
              <div className="p-4 rounded-xl border border-gray-200">
                {loadingCaptcha ? (
                  <Spin
                    className="my-3 mx-2"
                    indicator={<Loader className="animate-spin" />}
                  />
                ) : (
                  <div className="flex items-center gap-1 mb-4">
                    <img
                      src={captcha}
                      className="h-16 w-48 rounded border-2 border-white shadow-sm"
                    />
                    <Button
                      className="hover:bg-gray-200 "
                      icon={<RefreshCcw size={20} className="text-gray-600" />}
                      onClick={reloadCaptcha}
                    />
                  </div>
                )}
                <Input
                  className="text-sm"
                  placeholder={t("login.placeholder.captcha")}
                />
              </div>
            </Form.Item>

            <Form.Item className="mb-2">
              <Button
                loading={loading}
                type="primary"
                htmlType="submit"
                className="w-full h-12 text-sm text-md font-medium shadow-md hover:shadow-lg transition-all duration-300"
              >
                {t("login.signIn") || "Sign In"}
              </Button>
            </Form.Item>

            <div className="text-center space-y-4">
              <Link
                to="/reset-password"
                className="text-red-600 hover:text-red-500 hover:underline transition-colors"
              >
                {t("login.forgotPassword")}
              </Link>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default Login;
