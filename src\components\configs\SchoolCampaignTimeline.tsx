import {Timeline, Tag, Empty, Typography} from "antd";
import moment from "moment";
import {useTranslation} from "react-i18next";

const SchoolCampaignTimeline:any = ({ salesPeriods }: any) => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    return (
        <>
            Periodes des ventes

                {
                    salesPeriods && salesPeriods.length > 0 ? (
                        <Timeline className="pt-8 my-2 text-sm" mode={"alternate"}>
                            {
                                salesPeriods.map((period: any) => (
                                    <Timeline.Item key={period.id} color={period.status ? "green" : "red"}>
                                        <div className="text-xs mb-2">
                                            <strong>{period[`nom_${currentLang}`]}</strong>
                                        </div>
                                        <div className="text-xs mb-2">
                                            {moment(period.date_start).format("DD MMM YYYY")} - {moment(period.date_end).format("DD MMM YYYY")}
                                        </div>
                                        <Tag className="text-xs mb-2" color={period.status ? "green" : "red"}>
                                            {period.status ? "Ouvert" : "Fermé"}
                                        </Tag>
                                    </Timeline.Item>
                                ))
                            }
                        </Timeline>
                        ) : (
                        <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                                <Typography.Text type="secondary">
                                    {t("manage_campaigns.noSalesPeriods")}
                                </Typography.Text>
                            }
                            className="py-8"
                        />
                    )

                }
        </>
    );
};

export default SchoolCampaignTimeline;
