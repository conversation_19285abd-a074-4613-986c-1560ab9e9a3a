import { useRef, useState, useEffect } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    InputNumber,
    Switch,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
    getTypeVehicleTypeLocations,
    storeTypeVehicleTypeLocation,
    updateTypeVehicleTypeLocation,
    deleteTypeVehicleTypeLocation,
} from "../../../features/admin/typeVehicleTypeLocationSlice";
import { getTypeVehiculesAll } from "../../../features/admin/typeVehiculeSlice";
import { getLocationTypesAll } from "../../../features/admin/locationTypeSlice";

function ManageTypeVehicleTypeLocations() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingRelation, setEditingRelation] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    const [vehicleTypes, setVehicleTypes] = useState<any[]>([]);
    const [locationTypes, setLocationTypes] = useState<any[]>([]);


    /*|--------------------------------------------------------------------------
    | FETCH ALL RELATIONS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetRelations = (params: any, sort: any, filter: any) => {
        return dispatch(getTypeVehicleTypeLocations({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }
    useEffect(() => {
        const fetchData = async () => {
            try {
                const vehicleTypesResult = await dispatch(getTypeVehiculesAll()).unwrap();
                setVehicleTypes(vehicleTypesResult.data || []);

                const locationTypesResult = await dispatch(getLocationTypesAll()).unwrap();
                setLocationTypes(locationTypesResult.data || []);
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        fetchData();
    }, [dispatch]);

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_typeVehicleTypeLocations.labels.vehicleType")}`,
            dataIndex: "id_type_vehicule",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.type_vehicule?.[`nom_${currentLang}`] || '-',
            valueEnum: vehicleTypes.reduce((acc: any, type: any) => {
                acc[type.id] = { text: type[`nom_${currentLang}`] };
                return acc;
            }, {}),
        },
        {
            title: `${t("manage_typeVehicleTypeLocations.labels.locationType")}`,
            dataIndex: "id_type_location",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.location_type?.[`nom_${currentLang}`] || record.locationType?.code || '-',
            valueEnum: locationTypes.reduce((acc: any, type: any) => {
                acc[type.id] = { text: type[`nom_${currentLang}`] || type.code };
                return acc;
            }, {}),
        },
        {
            title: `${t("manage_typeVehicleTypeLocations.labels.minKm")}`,
            dataIndex: "km_min",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.km_min,
        },
        {
            title: `${t("manage_typeVehicleTypeLocations.labels.status")}`,
            dataIndex: "status",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <Switch
                    checked={record.status}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": { 
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": { 
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_typeVehicleTypeLocations.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_typeVehicleTypeLocations.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_typeVehicleTypeLocations.yes")}
                        cancelText={t("manage_typeVehicleTypeLocations.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/admin-dashboard">{t("auth_sidebar.categories.busLocations")}</Link>,
        },
        {
            title: t("manage_typeVehicleTypeLocations.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingRelation(record);
        form.setFieldsValue({
            id: record.id,
            id_type_vehicule: record.id_type_vehicule,
            id_type_location: record.id_type_location,
            km_min: record.km_min,
            status: record.status
        });
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit = (record: any) => {
        setEditingRelation(record);
        form.setFieldsValue({
            id: record.id,
            id_type_vehicule: record.id_type_vehicule,
            id_type_location: record.id_type_location,
            km_min: record.km_min,
            status: record.status
        });
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingRelation(null);
        form.resetFields();
        form.setFieldsValue({
            status: true
        });
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE RELATION
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingRelation ? { id: editingRelation.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingRelation) {
                await dispatch(updateTypeVehicleTypeLocation(payload)).unwrap();
            } else {
                await dispatch(storeTypeVehicleTypeLocation(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_typeVehicleTypeLocations.confirmAction"),
            content: editingRelation
                ? t("manage_typeVehicleTypeLocations.confirmUpdate")
                : t("manage_typeVehicleTypeLocations.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE RELATION
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteTypeVehicleTypeLocation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
     |  - HANDLE RESET
     |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_typeVehicleTypeLocations.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetRelations(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        sortDirections={["ascend", "descend"]}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="add" onClick={handleAdd} className="btn-add">
                                {t("manage_typeVehicleTypeLocations.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                open={modalVisible}
                title={
                    viewMode
                        ? t("manage_typeVehicleTypeLocations.details")
                        : editingRelation
                            ? t("manage_typeVehicleTypeLocations.edit")
                            : t("manage_typeVehicleTypeLocations.add")
                }
                onCancel={handleReset}
                footer={
                    viewMode
                        ? [
                            <Button key="close" onClick={handleReset}>
                                {t("common.close")}
                            </Button>,
                        ]
                        : [
                            <Button key="cancel" onClick={handleReset}>
                                {t("common.cancel")}
                            </Button>,
                            <Button
                                key="submit"
                                type="primary"
                                loading={loading}
                                onClick={() => form.submit()}
                            >
                                {t("common.save")}
                            </Button>,
                        ]
                }
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={viewMode}
                >
                    {editingRelation && (
                        <Form.Item
                            label={t("manage_typeVehicleTypeLocations.labels.id")}
                            name="id"
                        >
                            <Input disabled />
                        </Form.Item>
                    )}

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_typeVehicleTypeLocations.labels.vehicleType")}
                                name="id_type_vehicule"
                                rules={[{ required: true, message: t("manage_typeVehicleTypeLocations.errors.vehicleTypeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_typeVehicleTypeLocations.placeholders.selectVehicleType")}
                                    disabled={viewMode}
                                    showSearch
                                    optionFilterProp="children"
                                >
                                    {vehicleTypes.map(type => (
                                        <Select.Option key={type.id} value={type.id}>
                                            {type[`nom_${currentLang}`] || type.code}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_typeVehicleTypeLocations.labels.locationType")}
                                name="id_type_location"
                                rules={[{ required: true, message: t("manage_typeVehicleTypeLocations.errors.locationTypeRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_typeVehicleTypeLocations.placeholders.selectLocationType")}
                                    disabled={viewMode}
                                    showSearch
                                    optionFilterProp="children"
                                >
                                    {locationTypes.map(type => (
                                        <Select.Option key={type.id} value={type.id}>
                                            {type[`nom_${currentLang}`] || type.code}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_typeVehicleTypeLocations.labels.minKm")}
                                name="km_min"
                                rules={[
                                    { required: true, message: t("manage_typeVehicleTypeLocations.errors.minKmRequired") },
                                    { type: 'number', min: 0, message: t("manage_typeVehicleTypeLocations.errors.minKmPositive") }
                                ]}
                            >
                                <InputNumber
                                    style={{ width: '100%' }}
                                    min={0}
                                    disabled={viewMode}
                                    placeholder={t("manage_typeVehicleTypeLocations.placeholders.enterMinKm")}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_typeVehicleTypeLocations.labels.status")}
                                name="status"
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Switch disabled={viewMode} />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageTypeVehicleTypeLocations;
